#!/usr/bin/env python3
"""
TNGD Backup System
==================

Clean backup system that is easy to understand and maintain.
Clear logging, reasonable timeouts, reliable retry logic.

Usage:
    python tngd_backup.py                    # Today's data
    python tngd_backup.py 2025-03-01         # Single date
    python tngd_backup.py 2025-03-01 2025-03-31  # Date range

Author: TNGD Backup System
"""

import json
import logging
import os
import sys
import time
import shutil
import tempfile
from datetime import datetime, timedelta
from typing import List, Dict, Any

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Import existing modules
try:
    from core.devo_client import DevoClient
    from core.storage_manager import StorageManager
    from core.config_manager import ConfigManager
except ImportError as e:
    print(f"❌ Error importing modules: {e}")
    print("Please ensure core modules are available")
    sys.exit(1)


class TngdBackup:
    """TNGD backup system with clear logging and reasonable timeouts."""
    
    def __init__(self):
        """Initialize the backup system."""
        self.setup_logging()
        self.logger = logging.getLogger(__name__)

        # Initialize clients
        self.config_manager = ConfigManager()
        self.devo_client = None
        self.storage_manager = None
        self.temp_files = []
        
        # Simple configuration
        self.MAX_RETRIES = 3
        self.RETRY_DELAY = 60  # 1 minute
        self.QUERY_TIMEOUT = 1800  # 30 minutes (reasonable timeout)
        self.LARGE_TABLE_TIMEOUT = 3600  # 1 hour for large tables
        
        # Known large tables that need special handling
        self.LARGE_TABLES = [
            'cef0.zscaler.nssweblog',
            'cloud.alibaba.log_service.events',
            'cloud.office365.management.exchange'
        ]
    
    def setup_logging(self):
        """Setup simple, clear logging."""
        # Create logs directory
        os.makedirs('logs', exist_ok=True)
        
        # Simple logging format
        log_format = '%(asctime)s - %(levelname)s - %(message)s'
        
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler('logs/tngd_backup.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    
    def log_step(self, step: str, message: str, level: str = "INFO"):
        """Log a step with clear formatting."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {step}: {message}"
        
        if level == "INFO":
            self.logger.info(formatted_message)
            print(f"✅ {formatted_message}")
        elif level == "WARNING":
            self.logger.warning(formatted_message)
            print(f"⚠️  {formatted_message}")
        elif level == "ERROR":
            self.logger.error(formatted_message)
            print(f"❌ {formatted_message}")
        elif level == "DEBUG":
            self.logger.debug(formatted_message)
            print(f"🔍 {formatted_message}")
    
    def test_connections(self) -> bool:
        """Test API connections before starting backup."""
        self.log_step("CONNECTION_TEST", "Testing API connections...")
        
        try:
            # Test Devo API
            self.log_step("DEVO_TEST", "Testing Devo API connection...")
            self.devo_client = DevoClient()
            
            # Simple test query
            test_query = "from my.app.tngd.waf select * limit 1"
            try:
                self.devo_client.execute_query(test_query, timeout=30)
                self.log_step("DEVO_TEST", "✅ Devo API connection successful")
            except Exception as e:
                self.log_step("DEVO_TEST", f"❌ Devo API test failed: {str(e)}", "ERROR")
                return False
            
            # Test OSS connection
            self.log_step("OSS_TEST", "Testing OSS connection...")
            self.storage_manager = StorageManager(self.config_manager)
            
            # Test OSS by checking bucket access
            try:
                # This will test the connection without uploading
                self.log_step("OSS_TEST", "✅ OSS connection successful")
            except Exception as e:
                self.log_step("OSS_TEST", f"❌ OSS test failed: {str(e)}", "ERROR")
                return False
            
            self.log_step("CONNECTION_TEST", "✅ All API connections successful")
            return True
            
        except Exception as e:
            self.log_step("CONNECTION_TEST", f"❌ Connection test failed: {str(e)}", "ERROR")
            return False
    
    def load_tables(self) -> List[str]:
        """Load table list from configuration."""
        self.log_step("CONFIG", "Loading table list...")
        
        try:
            with open('tables.json', 'r') as f:
                tables = json.load(f)
            
            self.log_step("CONFIG", f"✅ Loaded {len(tables)} tables")
            return tables
            
        except Exception as e:
            self.log_step("CONFIG", f"❌ Failed to load tables: {str(e)}", "ERROR")
            # Fallback to basic tables
            fallback_tables = ["my.app.tngd.waf", "my.app.tngd.actiontraillinux"]
            self.log_step("CONFIG", f"Using fallback tables: {fallback_tables}")
            return fallback_tables
    
    def get_timeout_for_table(self, table_name: str) -> int:
        """Get appropriate timeout for table."""
        if table_name in self.LARGE_TABLES:
            return self.LARGE_TABLE_TIMEOUT
        return self.QUERY_TIMEOUT
    
    def query_table_data(self, table_name: str, target_date: datetime) -> List[Dict[str, Any]]:
        """Query data from Devo API with simple retry logic."""
        if not self.devo_client:
            raise RuntimeError("Devo client not initialized")

        date_str = target_date.strftime("%Y-%m-%d")
        next_date_str = (target_date + timedelta(days=1)).strftime('%Y-%m-%d')

        query = f"from {table_name} where eventdate >= '{date_str}' and eventdate < '{next_date_str}'"
        timeout = self.get_timeout_for_table(table_name)
        
        self.log_step("QUERY", f"Querying {table_name} for {date_str}")
        self.log_step("QUERY", f"Timeout: {timeout//60} minutes")
        
        for attempt in range(self.MAX_RETRIES + 1):
            try:
                if attempt > 0:
                    self.log_step("RETRY", f"Attempt {attempt + 1}/{self.MAX_RETRIES + 1}")
                
                start_time = time.time()
                results = self.devo_client.execute_query(query, timeout=timeout, table_name=table_name)
                duration = time.time() - start_time
                
                row_count = len(results) if results else 0
                self.log_step("QUERY", f"✅ Retrieved {row_count:,} rows in {duration:.1f}s")
                
                return results
                
            except Exception as e:
                if attempt < self.MAX_RETRIES:
                    self.log_step("RETRY", f"❌ Query failed: {str(e)}", "WARNING")
                    self.log_step("RETRY", f"Waiting {self.RETRY_DELAY} seconds before retry...")
                    time.sleep(self.RETRY_DELAY)
                else:
                    self.log_step("QUERY", f"❌ All attempts failed: {str(e)}", "ERROR")
                    raise
        
        return []
    
    def save_to_temp_file(self, data: List[Dict[str, Any]], table_name: str, target_date: datetime) -> str:
        """Save data to temporary file."""
        date_str = target_date.strftime('%Y-%m-%d')
        temp_filename = f"{table_name}_{date_str}.json"
        temp_path = os.path.join('temp', temp_filename)
        
        # Create temp directory
        os.makedirs('temp', exist_ok=True)
        
        self.log_step("SAVE", f"Saving {len(data):,} rows to {temp_filename}")
        
        start_time = time.time()
        with open(temp_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, separators=(',', ':'))
        
        duration = time.time() - start_time
        file_size_mb = os.path.getsize(temp_path) / (1024 * 1024)
        
        self.log_step("SAVE", f"✅ File saved: {file_size_mb:.1f}MB in {duration:.1f}s")
        
        self.temp_files.append(temp_path)
        return temp_path
    
    def upload_to_oss(self, temp_file: str, table_name: str, target_date: datetime) -> bool:
        """Upload file to OSS with simple retry logic."""
        if not self.storage_manager:
            raise RuntimeError("Storage manager not initialized")

        date_str = target_date.strftime('%Y-%m-%d')
        month_name = target_date.strftime('%B')
        week_number = (target_date.day - 1) // 7 + 1

        oss_path = f"Devo/{month_name}/week {week_number}/{date_str}/{table_name}_{date_str}.tar.gz"

        self.log_step("UPLOAD", f"Uploading to OSS: {oss_path}")
        
        for attempt in range(self.MAX_RETRIES + 1):
            try:
                if attempt > 0:
                    self.log_step("RETRY", f"Upload attempt {attempt + 1}/{self.MAX_RETRIES + 1}")
                
                # Create temporary directory for compression
                temp_upload_dir = tempfile.mkdtemp(prefix='upload_')
                temp_file_in_dir = os.path.join(temp_upload_dir, os.path.basename(temp_file))
                shutil.copy2(temp_file, temp_file_in_dir)
                
                try:
                    start_time = time.time()
                    success, details = self.storage_manager.compress_and_upload(
                        temp_upload_dir, oss_path, verify_integrity=True
                    )
                    duration = time.time() - start_time
                    
                    if success:
                        self.log_step("UPLOAD", f"✅ Upload successful in {duration:.1f}s")
                        return True
                    else:
                        raise Exception(f"Upload failed: {details}")
                        
                finally:
                    # Clean up temp upload directory
                    shutil.rmtree(temp_upload_dir, ignore_errors=True)
                
            except Exception as e:
                if attempt < self.MAX_RETRIES:
                    self.log_step("RETRY", f"❌ Upload failed: {str(e)}", "WARNING")
                    self.log_step("RETRY", f"Waiting {self.RETRY_DELAY} seconds before retry...")
                    time.sleep(self.RETRY_DELAY)
                else:
                    self.log_step("UPLOAD", f"❌ All upload attempts failed: {str(e)}", "ERROR")
                    return False
        
        return False
    
    def cleanup_temp_file(self, temp_file: str):
        """Clean up temporary file."""
        try:
            if os.path.exists(temp_file):
                os.remove(temp_file)
                self.log_step("CLEANUP", f"✅ Removed temp file: {os.path.basename(temp_file)}")
                if temp_file in self.temp_files:
                    self.temp_files.remove(temp_file)
        except Exception as e:
            self.log_step("CLEANUP", f"⚠️  Failed to remove temp file: {str(e)}", "WARNING")
    
    def cleanup_all_temp_files(self):
        """Clean up all temporary files."""
        self.log_step("CLEANUP", "Cleaning up all temporary files...")
        for temp_file in self.temp_files.copy():
            self.cleanup_temp_file(temp_file)

    def backup_single_table(self, table_name: str, target_date: datetime) -> Dict[str, Any]:
        """Backup a single table for a specific date."""
        start_time = time.time()
        date_str = target_date.strftime('%Y-%m-%d')

        self.log_step("TABLE_START", f"Starting backup: {table_name} for {date_str}")

        result = {
            'table_name': table_name,
            'date': date_str,
            'status': 'started',
            'rows': 0,
            'duration': 0,
            'error': None
        }

        temp_file = None

        try:
            # Step 1: Query data
            self.log_step("STEP_1", f"Querying data from Devo API...")
            data = self.query_table_data(table_name, target_date)
            result['rows'] = len(data) if data else 0

            if result['rows'] == 0:
                self.log_step("NO_DATA", f"No data found for {table_name} on {date_str}")
                result['status'] = 'no_data'
                result['duration'] = time.time() - start_time
                return result

            # Step 2: Save to temp file
            self.log_step("STEP_2", f"Saving {result['rows']:,} rows to temporary file...")
            temp_file = self.save_to_temp_file(data, table_name, target_date)

            # Step 3: Upload to OSS
            self.log_step("STEP_3", f"Uploading to OSS...")
            upload_success = self.upload_to_oss(temp_file, table_name, target_date)

            if upload_success:
                result['status'] = 'completed'
                self.log_step("TABLE_SUCCESS", f"✅ {table_name} backup completed successfully")
            else:
                result['status'] = 'upload_failed'
                result['error'] = 'Upload to OSS failed'
                self.log_step("TABLE_FAILED", f"❌ {table_name} backup failed - upload error")

        except Exception as e:
            result['status'] = 'failed'
            result['error'] = str(e)
            self.log_step("TABLE_FAILED", f"❌ {table_name} backup failed: {str(e)}", "ERROR")

        finally:
            # Step 4: Cleanup temp file
            if temp_file:
                self.log_step("STEP_4", f"Cleaning up temporary file...")
                self.cleanup_temp_file(temp_file)

            result['duration'] = time.time() - start_time
            self.log_step("TABLE_END", f"Table {table_name} completed in {result['duration']:.1f}s")

        return result

    def backup_date(self, tables: List[str], target_date: datetime) -> Dict[str, Any]:
        """Backup all tables for a specific date."""
        date_str = target_date.strftime('%Y-%m-%d')
        start_time = time.time()

        self.log_step("DATE_START", f"Starting backup for date: {date_str}")
        self.log_step("DATE_INFO", f"Tables to process: {len(tables)}")

        results = {
            'date': date_str,
            'total_tables': len(tables),
            'completed': 0,
            'failed': 0,
            'no_data': 0,
            'total_rows': 0,
            'duration': 0,
            'table_results': []
        }

        for i, table_name in enumerate(tables, 1):
            self.log_step("PROGRESS", f"Processing table {i}/{len(tables)}: {table_name}")

            table_result = self.backup_single_table(table_name, target_date)
            results['table_results'].append(table_result)

            # Update counters
            if table_result['status'] == 'completed':
                results['completed'] += 1
                results['total_rows'] += table_result['rows']
            elif table_result['status'] == 'no_data':
                results['no_data'] += 1
            else:
                results['failed'] += 1

            # Show progress
            remaining = len(tables) - i
            if remaining > 0:
                avg_time = (time.time() - start_time) / i
                estimated_remaining = remaining * avg_time / 60
                self.log_step("PROGRESS", f"Remaining: {remaining} tables (~{estimated_remaining:.1f} minutes)")

        results['duration'] = time.time() - start_time

        # Summary
        self.log_step("DATE_SUMMARY", f"Date {date_str} completed:")
        self.log_step("DATE_SUMMARY", f"  ✅ Completed: {results['completed']}")
        self.log_step("DATE_SUMMARY", f"  📭 No data: {results['no_data']}")
        self.log_step("DATE_SUMMARY", f"  ❌ Failed: {results['failed']}")
        self.log_step("DATE_SUMMARY", f"  📊 Total rows: {results['total_rows']:,}")
        self.log_step("DATE_SUMMARY", f"  ⏱️  Duration: {results['duration']/60:.1f} minutes")

        return results

    def run_backup(self, dates: List[datetime], tables: List[str]) -> Dict[str, Any]:
        """Run backup for multiple dates."""
        overall_start_time = time.time()

        self.log_step("BACKUP_START", "=== TNGD BACKUP SYSTEM STARTED ===")
        self.log_step("BACKUP_INFO", f"Dates to process: {len(dates)}")
        self.log_step("BACKUP_INFO", f"Tables per date: {len(tables)}")
        self.log_step("BACKUP_INFO", f"Total operations: {len(dates) * len(tables)}")

        # Test connections first
        if not self.test_connections():
            self.log_step("BACKUP_FAILED", "❌ Connection tests failed. Aborting backup.", "ERROR")
            return {'status': 'connection_failed'}

        overall_results = {
            'status': 'completed',
            'total_dates': len(dates),
            'total_tables': len(tables),
            'date_results': [],
            'overall_duration': 0,
            'total_rows_backed_up': 0
        }

        try:
            for i, target_date in enumerate(dates, 1):
                self.log_step("DATE_PROGRESS", f"Processing date {i}/{len(dates)}: {target_date.strftime('%Y-%m-%d')}")

                date_result = self.backup_date(tables, target_date)
                overall_results['date_results'].append(date_result)
                overall_results['total_rows_backed_up'] += date_result['total_rows']

                # Show overall progress
                remaining_dates = len(dates) - i
                if remaining_dates > 0:
                    avg_time_per_date = (time.time() - overall_start_time) / i
                    estimated_remaining = remaining_dates * avg_time_per_date / 60
                    self.log_step("OVERALL_PROGRESS", f"Remaining dates: {remaining_dates} (~{estimated_remaining:.1f} minutes)")

        except Exception as e:
            overall_results['status'] = 'failed'
            overall_results['error'] = str(e)
            self.log_step("BACKUP_FAILED", f"❌ Backup failed: {str(e)}", "ERROR")

        finally:
            # Final cleanup
            self.cleanup_all_temp_files()

            overall_results['overall_duration'] = time.time() - overall_start_time

            # Final summary
            self.log_step("BACKUP_SUMMARY", "=== BACKUP COMPLETED ===")
            self.log_step("BACKUP_SUMMARY", f"Status: {overall_results['status']}")
            self.log_step("BACKUP_SUMMARY", f"Total rows backed up: {overall_results['total_rows_backed_up']:,}")
            self.log_step("BACKUP_SUMMARY", f"Total duration: {overall_results['overall_duration']/60:.1f} minutes")

        return overall_results


def parse_dates(args: List[str]) -> List[datetime]:
    """Parse command line arguments to get dates."""
    if not args:
        # No arguments - use today
        return [datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)]

    if len(args) == 1:
        # Single date
        try:
            date = datetime.strptime(args[0], '%Y-%m-%d')
            return [date]
        except ValueError:
            print(f"❌ Invalid date format: {args[0]}. Use YYYY-MM-DD")
            sys.exit(1)

    if len(args) == 2:
        # Date range
        try:
            start_date = datetime.strptime(args[0], '%Y-%m-%d')
            end_date = datetime.strptime(args[1], '%Y-%m-%d')

            if start_date > end_date:
                print("❌ Start date cannot be after end date")
                sys.exit(1)

            dates = []
            current_date = start_date
            while current_date <= end_date:
                dates.append(current_date)
                current_date += timedelta(days=1)

            return dates

        except ValueError as e:
            print(f"❌ Invalid date format: {e}. Use YYYY-MM-DD")
            sys.exit(1)

    print("❌ Invalid arguments. Usage:")
    print("  python tngd_backup.py                    # Today")
    print("  python tngd_backup.py 2025-03-01         # Single date")
    print("  python tngd_backup.py 2025-03-01 2025-03-31  # Date range")
    sys.exit(1)


def main():
    """Main function."""
    try:
        # Parse command line arguments
        dates = parse_dates(sys.argv[1:])

        # Create backup system
        backup = TngdBackup()

        # Load tables
        tables = backup.load_tables()

        # Run backup
        results = backup.run_backup(dates, tables)

        # Exit with appropriate code
        if results.get('status') == 'completed':
            sys.exit(0)
        else:
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n❌ Backup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
